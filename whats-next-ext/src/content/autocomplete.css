/* AI Autocomplete Ghost Overlay */
.ai-autocomplete-ghost-overlay {
  position: fixed;
  pointer-events: none;
  z-index: 999999;
  background: transparent;
  border: none;
  outline: none;
  white-space: pre;
  overflow: hidden;
  word-wrap: break-word;
  line-height: inherit;
  font-family: inherit;
  font-size: inherit;
  font-weight: inherit;
  letter-spacing: inherit;
  text-align: left;
  text-indent: inherit;
  text-transform: inherit;
  direction: inherit;
  unicode-bidi: inherit;
}

/* Dark mode support for ghost text */
@media (prefers-color-scheme: dark) {
  .ai-autocomplete-ghost-overlay span:last-child {
    color: #666 !important;
    opacity: 0.7 !important;
  }
}

/* Debug mode - make ghost text more visible for testing */
.ai-autocomplete-debug .ai-autocomplete-ghost-overlay span:last-child {
  color: #ff6b6b !important;
  opacity: 0.8 !important;
  background-color: rgba(255, 107, 107, 0.1) !important;
}

/* Ensure ghost overlay doesn't interfere with page layout */
.ai-autocomplete-ghost-overlay {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}
