import { useState, useEffect } from 'react'
import './App.css'

interface DebugInfo {
  extensionActive: boolean;
  currentTab?: string;
  inputsFound: number;
  lastSuggestion?: string;
  apiStatus: 'unknown' | 'working' | 'error';
}

export default function App() {
  const [debugInfo, setDebugInfo] = useState<DebugInfo>({
    extensionActive: false,
    inputsFound: 0,
    apiStatus: 'unknown'
  });
  const [logs, setLogs] = useState<string[]>([]);

  useEffect(() => {
    // Get current tab info
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs[0]) {
        setDebugInfo(prev => ({
          ...prev,
          currentTab: tabs[0].url
        }));

        // Test if content script is loaded by sending a message
        chrome.tabs.sendMessage(tabs[0].id!, { type: 'ping' }, (_response) => {
          if (chrome.runtime.lastError) {
            setDebugInfo(prev => ({
              ...prev,
              extensionActive: false
            }));
          } else {
            setDebugInfo(prev => ({
              ...prev,
              extensionActive: true
            }));
          }
        });
      }
    });

    // Test API connection
    testApiConnection();
  }, []);

  const testApiConnection = () => {
    chrome.runtime.sendMessage(
      {
        type: 'getSuggestion',
        text: 'Hello world',
        pageContext: { title: 'Test', h1: 'Test' }
      },
      (response) => {
        if (chrome.runtime.lastError) {
          setDebugInfo(prev => ({ ...prev, apiStatus: 'error' }));
          addLog(`API Error: ${chrome.runtime.lastError.message}`);
        } else if (response?.suggestion) {
          setDebugInfo(prev => ({
            ...prev,
            apiStatus: 'working',
            lastSuggestion: response.suggestion
          }));
          addLog(`API Test Success: ${response.suggestion}`);
        } else {
          setDebugInfo(prev => ({ ...prev, apiStatus: 'error' }));
          addLog('API Test Failed: No response');
        }
      }
    );
  };

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 9)]);
  };

  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <div style={{ width: '400px', padding: '16px' }}>
      <h2>AI Autocomplete Debug</h2>

      <div style={{ marginBottom: '16px' }}>
        <h3>Status</h3>
        <div style={{ fontSize: '14px' }}>
          <div>Extension Active: <span style={{ color: debugInfo.extensionActive ? 'green' : 'red' }}>
            {debugInfo.extensionActive ? '✓ Yes' : '✗ No'}
          </span></div>
          <div>API Status: <span style={{ color: debugInfo.apiStatus === 'working' ? 'green' : debugInfo.apiStatus === 'error' ? 'red' : 'orange' }}>
            {debugInfo.apiStatus === 'working' ? '✓ Working' : debugInfo.apiStatus === 'error' ? '✗ Error' : '? Unknown'}
          </span></div>
          <div>Current Tab: <span style={{ fontSize: '12px', wordBreak: 'break-all' }}>
            {debugInfo.currentTab}
          </span></div>
        </div>
      </div>

      <div style={{ marginBottom: '16px' }}>
        <h3>Actions</h3>
        <button onClick={testApiConnection} style={{ marginRight: '8px' }}>
          Test API
        </button>
        <button onClick={clearLogs}>
          Clear Logs
        </button>
      </div>

      <div>
        <h3>Recent Logs</h3>
        <div style={{
          height: '200px',
          overflow: 'auto',
          border: '1px solid #ccc',
          padding: '8px',
          fontSize: '12px',
          backgroundColor: '#f5f5f5'
        }}>
          {logs.length === 0 ? (
            <div style={{ color: '#666' }}>No logs yet. Try typing in an input field on a webpage.</div>
          ) : (
            logs.map((log, index) => (
              <div key={index} style={{ marginBottom: '4px' }}>
                {log}
              </div>
            ))
          )}
        </div>
      </div>

      <div style={{ marginTop: '16px', fontSize: '12px', color: '#666' }}>
        <p>To debug:</p>
        <ol>
          <li>Open Developer Tools (F12)</li>
          <li>Go to Console tab</li>
          <li>Look for "[AI Autocomplete]" messages</li>
          <li>Try typing in text fields on websites</li>
        </ol>
      </div>
    </div>
  )
}
