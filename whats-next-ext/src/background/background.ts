import { GoogleGenAI } from "@google/genai";
const apiKey = "AIzaSyAYFyMAWmFQwQhCV16zaNVEX5T60yGCD2s"; // Store securely in production

const ai = new GoogleGenAI({ apiKey });

// Define interfaces for type safety
interface PageContext {
  title: string;
  h1: string;
}

interface MessagePayload {
  type: "getSuggestion";
  text: string;
  pageContext: PageContext;
}

// Debug logging function
function debugLog(message: string, data?: any) {
  console.log(`[AI Autocomplete Background] ${message}`, data || "");
}

// Initialize background script
debugLog("Background script initialized");

chrome.runtime.onMessage.addListener(
  (request: MessagePayload, sender, sendResponse) => {
    debugLog("Received message:", {
      type: request.type,
      sender: sender.tab?.url,
    });

    if (request.type === "getSuggestion") {
      debugLog("Processing getSuggestion request:", {
        textLength: request.text.length,
        text:
          request.text.substring(0, 50) +
          (request.text.length > 50 ? "..." : ""),
        pageContext: request.pageContext,
      });
      const { text, pageContext } = request;

      // Create a detailed prompt using the page context for better suggestions
      const prompt = `You are an intelligent autocomplete assistant. Complete the following text naturally and contextually.

Context:
- Page: "${pageContext.title}"
- Section: "${pageContext.h1}"

Text to complete: "${text}"

Rules:
- Only provide the completion, no explanations
- Keep it concise (1-10 words max)
- Match the writing style and tone
- Be contextually relevant to the page content
- Don't repeat the input text

Completion:`;

      debugLog("Making API request to Gemini");
      ai.models
        .generateContent({
          model: "gemini-2.5-flash",
          contents: prompt,
        })
        .then((data) => {
          debugLog("API response data:", data.text);
          if (data.text) {
            let completion = data.text;
            debugLog("Raw completion from API:", completion);

            // Clean up the completion
            completion = completion.replace(/^["']|["']$/g, ""); // Remove quotes
            completion = completion.split("\n")[0]; // Take only first line
            debugLog("Cleaned completion:", completion);

            if (completion && completion.length > 0) {
              // Ensure proper spacing
              const needsSpace =
                !request.text.endsWith(" ") && !completion.startsWith(" ");
              const fullSuggestion =
                request.text + (needsSpace ? " " : "") + completion;
              debugLog("Sending suggestion:", fullSuggestion);
              sendResponse({ suggestion: fullSuggestion });
            } else {
              debugLog("Empty completion, sending empty response");
              sendResponse({});
            }
          } else {
            debugLog("No valid completion in response:", data);
            sendResponse({});
          }
        })
        .catch((error) => {
          debugLog("Error fetching from Gemini API:", error);
          sendResponse({}); // Send empty response on error
        });

      return true; // Indicates an asynchronous response
    }
  }
);
