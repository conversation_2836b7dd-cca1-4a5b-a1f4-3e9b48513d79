# 🐛 AI Autocomplete Extension - Debugging Guide

## 🚀 Quick Start Debugging

### 1. Load the Extension
1. Open Chrome and go to `chrome://extensions/`
2. Enable "Developer mode" (toggle in top-right)
3. Click "Load unpacked" and select the `dist` folder
4. The extension should appear with a green "Context-Aware AI Autocomplete" tile

### 2. Verify Extension is Loaded
- **Green Dot Indicator**: Look for a small green dot in the top-right corner of any webpage (appears for 5 seconds)
- **Extension Popup**: Click the extension icon in the toolbar to see debug information
- **Console Messages**: Open Developer Tools (F12) → Console tab, look for "[AI Autocomplete]" messages

### 3. Test the Extension
- Open the included `test-page.html` file in Chrome
- Follow the testing instructions on that page

## 🔍 Debugging Steps

### Step 1: Check Extension Loading
**Expected Console Messages:**
```
[AI Autocomplete Content] AutocompleteManager initializing...
[AI Autocomplete Content] Initializing autocomplete manager
[AI Autocomplete Content] Found X existing inputs to process
[AI Autocomplete Content] Processing input: {tagName: "INPUT", type: "text", ...}
[AI Autocomplete Content] Autocomplete manager initialized successfully
```

**If you don't see these messages:**
- Extension didn't load properly
- Check `chrome://extensions/` for errors
- Try reloading the extension

### Step 2: Check Input Detection
**When you focus on an input field:**
```
[AI Autocomplete Content] Focus in event: {tagName: "INPUT", ...}
[AI Autocomplete Content] Setting active input
```

**If you don't see focus messages:**
- The input might not be a supported type
- Check if it's disabled/readonly
- Try different input fields

### Step 3: Check Typing Detection
**When you type:**
```
[AI Autocomplete Content] Input event: {textLength: X, text: "your text..."}
[AI Autocomplete Content] Word count: X
```

**If you don't see input messages:**
- Event listeners might not be attached
- Try refreshing the page

### Step 4: Check API Trigger
**After 3+ words ending with space OR 800ms pause:**
```
[AI Autocomplete Content] Triggering immediate suggestion (word count >= 3 and ends with space)
OR
[AI Autocomplete Content] Setting debounce timer for suggestion
[AI Autocomplete Content] Fetching suggestion... {pageContext: {...}}
```

### Step 5: Check Background Script
**In background script console:**
```
[AI Autocomplete Background] Background script initialized
[AI Autocomplete Background] Received message: {type: "getSuggestion", ...}
[AI Autocomplete Background] Processing getSuggestion request: {...}
[AI Autocomplete Background] Making API request to Gemini
[AI Autocomplete Background] API response received: {status: 200, ok: true}
[AI Autocomplete Background] API response data: {...}
[AI Autocomplete Background] Raw completion from API: "your suggestion"
[AI Autocomplete Background] Cleaned completion: "your suggestion"
[AI Autocomplete Background] Sending suggestion: "your full text your suggestion"
```

### Step 6: Check API Response
**Back in content script:**
```
[AI Autocomplete Content] Received response from background: {suggestion: "..."}
[AI Autocomplete Content] Setting suggestion: "your full text your suggestion"
```

## 🛠️ Debug Tools

### Console Commands
Open Developer Tools → Console and try these:

```javascript
// Toggle debug mode (makes ghost text red and more visible)
aiAutocompleteDebug.toggle()

// Show extension status
aiAutocompleteDebug.status()

// Access the manager object directly
aiAutocompleteManager

// Check if extension is active
aiAutocompleteManager.activeInput

// Check current suggestion
aiAutocompleteManager.suggestion
```

### Extension Popup Debug Panel
Click the extension icon to see:
- ✅ Extension Active status
- ✅ API Status (working/error)
- 📝 Current tab URL
- 🧪 Test API button
- 📋 Recent logs

## 🚨 Common Issues & Solutions

### Issue: Extension not loading
**Symptoms:** No console messages, no green dot
**Solutions:**
- Check `chrome://extensions/` for errors
- Ensure you selected the `dist` folder, not the root folder
- Try disabling and re-enabling the extension

### Issue: No input detection
**Symptoms:** No "Processing input" messages
**Solutions:**
- Refresh the page after loading extension
- Check if inputs are disabled/readonly
- Try different input types (text, email, textarea)

### Issue: No API calls
**Symptoms:** No "Fetching suggestion" messages
**Solutions:**
- Type at least 3 words followed by a space
- Or wait 800ms after typing
- Check if you have an active internet connection

### Issue: API errors
**Symptoms:** "API Error" in popup or console errors
**Solutions:**
- Check if the Gemini API key is valid
- Verify API key has proper permissions
- Check network connectivity
- Look for CORS errors in console

### Issue: No ghost text visible
**Symptoms:** API works but no visual suggestions
**Solutions:**
- Enable debug mode: `aiAutocompleteDebug.toggle()`
- Check if ghost overlay is created
- Verify input field styling isn't interfering
- Try on the test page first

## 📊 Extension Popup Debug Info

The popup shows:
- **Extension Active**: Whether content script is loaded
- **API Status**: Whether Gemini API is responding
- **Current Tab**: URL of active tab
- **Test API Button**: Sends a test request
- **Clear Logs Button**: Clears the log display

## 🧪 Test Page

Use the included `test-page.html` for systematic testing:
1. Open the file in Chrome
2. Follow the step-by-step instructions
3. Use the debug buttons for quick testing
4. Check expected vs actual behavior

## 📞 Getting Help

If the extension still isn't working:
1. Open the test page
2. Open Developer Tools (F12)
3. Go to Console tab
4. Try typing in the test inputs
5. Copy all "[AI Autocomplete]" messages
6. Check the extension popup for status info
7. Report which step in the debugging process fails
