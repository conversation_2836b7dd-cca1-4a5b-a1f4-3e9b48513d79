<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Autocomplete Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        input, textarea {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 16px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        .instructions {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .debug-info {
            background-color: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 14px;
        }
        button {
            background-color: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #005a87;
        }
    </style>
</head>
<body>
    <h1>AI Autocomplete Extension Test Page</h1>
    
    <div class="instructions">
        <h3>🧪 Testing Instructions:</h3>
        <ol>
            <li><strong>Look for the green dot</strong> in the top-right corner (appears for 5 seconds when extension loads)</li>
            <li><strong>Open the extension popup</strong> to see debug information</li>
            <li><strong>Type in the fields below</strong> - after 3+ words ending with space, or after 800ms pause, you should see gray "ghost text"</li>
            <li><strong>Press Tab or Right Arrow</strong> to accept suggestions</li>
            <li><strong>Open Developer Tools (F12)</strong> and check Console for "[AI Autocomplete]" messages</li>
        </ol>
    </div>

    <div class="test-section">
        <h3>📝 Text Input Field</h3>
        <p>Try typing: "I am writing a blog post about"</p>
        <input type="text" placeholder="Start typing here..." id="test-input-1">
    </div>

    <div class="test-section">
        <h3>📄 Textarea Field</h3>
        <p>Try typing: "The weather today is beautiful and"</p>
        <textarea placeholder="Start typing here..." id="test-textarea-1"></textarea>
    </div>

    <div class="test-section">
        <h3>✉️ Email Input Field</h3>
        <p>Try typing: "Please send me information about"</p>
        <input type="email" placeholder="<EMAIL>" id="test-email-1">
    </div>

    <div class="test-section">
        <h3>🔍 Search Input Field</h3>
        <p>Try typing: "How to learn programming"</p>
        <input type="search" placeholder="Search..." id="test-search-1">
    </div>

    <div class="debug-info">
        <h3>🐛 Debug Commands (Open Console and type):</h3>
        <p><code>aiAutocompleteDebug.toggle()</code> - Toggle debug mode (makes ghost text red)</p>
        <p><code>aiAutocompleteDebug.status()</code> - Show extension status</p>
        <p><code>aiAutocompleteManager</code> - Access the manager object</p>
        
        <h4>Quick Debug Buttons:</h4>
        <button onclick="window.aiAutocompleteDebug?.toggle()">Toggle Debug Mode</button>
        <button onclick="window.aiAutocompleteDebug?.status()">Show Status</button>
        <button onclick="console.clear()">Clear Console</button>
    </div>

    <div class="test-section">
        <h3>🎯 Expected Behavior:</h3>
        <ul>
            <li>✅ Green dot appears briefly when page loads</li>
            <li>✅ Extension popup shows "Extension Active: ✓ Yes"</li>
            <li>✅ Console shows "[AI Autocomplete Content] AutocompleteManager initializing..."</li>
            <li>✅ Console shows "[AI Autocomplete Content] Found X existing inputs to process"</li>
            <li>✅ When you focus an input: "Focus in event" and "Setting active input"</li>
            <li>✅ When you type: "Input event", "Word count: X"</li>
            <li>✅ After 3+ words + space OR 800ms pause: "Fetching suggestion..."</li>
            <li>✅ API response: "Received response from background"</li>
            <li>✅ Gray ghost text appears after your cursor</li>
            <li>✅ Tab/Right Arrow accepts the suggestion</li>
        </ul>
    </div>

    <script>
        // Add some JavaScript to make the page more interactive
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Test page loaded. Extension should initialize now.');
            
            // Add event listeners to show when inputs are focused
            const inputs = document.querySelectorAll('input, textarea');
            inputs.forEach((input, index) => {
                input.addEventListener('focus', () => {
                    console.log(`📝 Focused input ${index + 1}: ${input.id}`);
                });
                
                input.addEventListener('input', (e) => {
                    const target = e.target as HTMLInputElement;
                    console.log(`⌨️  Input ${index + 1} value: "${target.value}"`);
                });
            });
        });
    </script>
</body>
</html>
