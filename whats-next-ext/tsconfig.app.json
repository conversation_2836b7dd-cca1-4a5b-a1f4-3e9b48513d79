{"compilerOptions": {"tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "target": "ES2020", "jsx": "react-jsx", "lib": ["ES2020", "DOM", "DOM.Iterable"], "moduleDetection": "force", "useDefineForClassFields": true, "baseUrl": ".", "module": "ESNext", "moduleResolution": "bundler", "paths": {"@/*": ["src/*"]}, "types": ["vite/client", "chrome"], "allowImportingTsExtensions": true, "strict": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true, "noEmit": true, "isolatedModules": true, "skipLibCheck": true, "noUncheckedSideEffectImports": true}, "include": ["src"]}