import { defineManifest } from '@crxjs/vite-plugin'
import pkg from './package.json'

export default defineManifest({
  manifest_version: 3,
  name: "Context-Aware AI Autocomplete",
  description: "Intelligent, context-aware autocomplete suggestions powered by Gemini AI",
  version: pkg.version,
  icons: {
    16: 'public/logo.png',
    32: 'public/logo.png',
    48: 'public/logo.png',
    128: 'public/logo.png',
  },
  action: {
    default_icon: {
      48: 'public/logo.png',
    },
    default_popup: 'src/popup/index.html',
    default_title: "Context-Aware AI Autocomplete"
  },
  permissions: [
    'sidePanel',
    'contentSettings',
    'storage',
    'activeTab',
    'scripting'
  ],
  host_permissions: [
    '<all_urls>',
    'https://generativelanguage.googleapis.com/*'
  ],
  background: {
    service_worker: 'src/background/background.ts',
    type: 'module'
  },
  content_scripts: [{
    js: ['src/content/main.tsx'],
    matches: ['<all_urls>'],
    run_at: 'document_end'
  }],
  side_panel: {
    default_path: 'src/sidepanel/index.html',
  },
})
