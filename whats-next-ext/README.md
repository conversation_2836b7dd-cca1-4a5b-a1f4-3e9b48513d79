# Context-Aware AI Autocomplete

A sophisticated browser extension that provides intelligent, context-aware autocomplete suggestions across the entire web. Moving beyond simple word prediction, this extension acts as a smart writing assistant that understands the context of the webpage you are on.

## Features

- **Universal Compatibility**: Works on virtually all websites and within most standard `<input>` and `<textarea>` fields
- **Inline "Ghost Text" UI**: Suggestions appear as light-gray text directly after the cursor, providing a non-intrusive experience
- **Context-Aware Predictions**: Analyzes webpage content (title, headings) to provide highly relevant suggestions
- **Smart Trigger Logic**: Uses both word count and debounce triggers to optimize API calls
- **Intuitive Interaction**: Accept suggestions with Tab or Right Arrow key
- **Modern Tech Stack**: Built with React, TypeScript, and Vite
- **Powered by Google Gemini AI**: Leverages advanced language models for intelligent completions

## Setup

1. **Install dependencies:**
```bash
npm install
```

2. **Configure API Key:**
   - Open `src/background/background.ts`
   - Replace `YOUR_API_KEY_HERE` with your Google Gemini API key
   - Get your API key from [Google AI Studio](https://makersuite.google.com/app/apikey)

3. **Start development server:**
```bash
npm run dev
```

4. **Load extension in Chrome:**
   - Open Chrome and navigate to `chrome://extensions/`
   - Enable "Developer mode"
   - Click "Load unpacked" and select the `dist` directory

5. **Build for production:**
```bash
npm run build
```

## How It Works

The extension operates on a decoupled architecture with three main components:

### Content Script (`src/content/main.tsx`)
- Scans the DOM for eligible input fields
- Creates React roots for each input field
- Injects the autocomplete functionality

### React Component (`src/content/AutocompleteInjector.tsx`)
- Manages state for user input and AI suggestions
- Handles smart trigger logic (word count + debouncing)
- Provides inline "ghost text" UI
- Copies original input styling for seamless integration

### Background Script (`src/background/background.ts`)
- Handles API communication with Google Gemini
- Constructs context-aware prompts using page information
- Manages secure API key storage

## Project Structure

- `src/background/` - Background service worker for API calls
- `src/content/` - Content scripts and autocomplete components
- `src/popup/` - Extension popup UI
- `src/sidepanel/` - Extension side panel UI
- `manifest.config.ts` - Chrome extension manifest configuration

## Usage

1. Navigate to any webpage with text input fields
2. Start typing in any `<input>` or `<textarea>` element
3. After typing 4+ words or pausing, you'll see gray "ghost text" suggestions
4. Press **Tab** or **Right Arrow** to accept the suggestion
5. The extension automatically considers page context (title, headings) for better suggestions

## Technical Details

- **Frontend**: React 18+ with TypeScript
- **Build Tool**: Vite with CRXJS plugin
- **AI Model**: Google Gemini Pro via REST API
- **Styling**: CSS with dynamic style copying for seamless integration
- **Triggers**: Hybrid approach using word count (4+ words) and debounce (500ms)

## Documentation

- [React Documentation](https://reactjs.org/)
- [Vite Documentation](https://vitejs.dev/)
- [CRXJS Documentation](https://crxjs.dev/vite-plugin)
- [Google Gemini API](https://ai.google.dev/docs)
